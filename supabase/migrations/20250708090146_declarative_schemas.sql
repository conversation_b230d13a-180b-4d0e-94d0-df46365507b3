drop trigger if exists "update_approved_changes_updated_at" on "public"."approved_changes";

drop trigger if exists "update_updated_at_blc" on "public"."budget_line_item_current";

drop trigger if exists "update_risk_register_updated_at" on "public"."risk_register";

drop policy "Organization members can insert a clients" on "public"."client";

drop policy "Organization members can update clients" on "public"."client";

drop policy "Project Editors and Owners can delete gateway checklist item" on "public"."gateway_checklist_item";

drop policy "Project Editors and Owners can insert gateway checklist item" on "public"."gateway_checklist_item";

drop policy "Project Editors and Owners can update gateway checklist item" on "public"."gateway_checklist_item";

drop policy "Admins and invitees can read invites" on "public"."invite";

drop policy "Admins and the invitee can update invites" on "public"."invite";

drop policy "Users can view memberships they have access to" on "public"."membership";

drop policy "System can insert WBS library item audit records" on "public"."wbs_library_item_audit";

drop policy "Users can view custom WBS library item audit for accessible ite" on "public"."wbs_library_item_audit";

drop policy "Users can view standard WBS library item audit records" on "public"."wbs_library_item_audit";

drop policy "Project editors can update budget line item" on "public"."budget_line_item_current";

drop policy "Project editors can update budget snapshot" on "public"."budget_snapshot";

drop policy "Project editors can update budget snapshot line item" on "public"."budget_snapshot_line_item";

drop policy "Users can view clients they have access to" on "public"."client";

drop policy "Users can view gateway checklist item audit for accessible proj" on "public"."gateway_checklist_item_audit";

drop policy "Admins can delete invites" on "public"."invite";

drop policy "Users can view organizations they have access to" on "public"."organization";

drop policy "Users can insert gateway stage info for projects they can edit" on "public"."project_gateway_stage_info";

drop policy "Users can view project gateway stage info audit for accessible " on "public"."project_gateway_stage_info_audit";

drop policy "Project Editors and Owners can update project stage" on "public"."project_stage";

alter table "public"."project" drop constraint "project_wbs_library_id_fkey";

alter table "public"."wbs_library_item" drop constraint "wbs_library_item_client_id_fkey";

alter table "public"."wbs_library_item" drop constraint "wbs_library_item_parent_item_id_fkey";

alter table "public"."wbs_library_item" drop constraint "wbs_library_item_project_id_fkey";

drop function if exists "public"."accept_invite"(token_param character);

drop function if exists "public"."complete_project_stage"(p_project_stage_id uuid, p_completion_notes text);

drop function if exists "public"."create_organization"(name text, description text, logo_url text);

drop function if exists "public"."get_client_members"(_client_name text);

drop function if exists "public"."get_clients_with_permissions"(org_name_param text);

drop function if exists "public"."get_organization_by_name"(org_name_param text);

drop function if exists "public"."profiles_with_client_access"(_client_name text);

drop function if exists "public"."profiles_with_project_access"(_project_name text, _client_name text);

drop index if exists "public"."approved_changes_original_risk_idx";

drop index if exists "public"."approved_changes_project_idx";

drop index if exists "public"."idx_approved_changes_audit_approved_change_id";

drop index if exists "public"."idx_approved_changes_audit_changed_at";

drop index if exists "public"."idx_approved_changes_audit_changed_by";

drop index if exists "public"."idx_approved_changes_audit_project_id";

drop index if exists "public"."idx_budget_line_item_audit_changed_at";

drop index if exists "public"."idx_budget_line_item_audit_changed_by";

drop index if exists "public"."idx_budget_line_item_audit_operation_type";

drop index if exists "public"."idx_budget_line_item_audit_project_id";

drop index if exists "public"."idx_budget_line_item_current_project_id";

drop index if exists "public"."idx_budget_line_item_current_wbs_library_item_id";

drop index if exists "public"."idx_budget_snapshot_line_item_wbs_library_item_id";

drop index if exists "public"."idx_gateway_checklist_item_audit_changed_at";

drop index if exists "public"."idx_gateway_checklist_item_audit_changed_by";

drop index if exists "public"."idx_gateway_checklist_item_audit_project_stage_id";

drop index if exists "public"."idx_project_gateway_stage_info_audit_changed_at";

drop index if exists "public"."idx_project_gateway_stage_info_audit_changed_by";

drop index if exists "public"."idx_project_gateway_stage_info_audit_project_stage_id";

drop index if exists "public"."idx_project_stage_audit_changed_at";

drop index if exists "public"."idx_project_stage_audit_changed_by";

drop index if exists "public"."idx_project_stage_audit_operation_type";

drop index if exists "public"."idx_project_stage_audit_project_id";

drop index if exists "public"."idx_risk_register_audit_changed_at";

drop index if exists "public"."idx_risk_register_audit_changed_by";

drop index if exists "public"."idx_risk_register_audit_project_id";

drop index if exists "public"."idx_risk_register_audit_risk_id";

drop index if exists "public"."idx_wbs_library_item_audit_changed_at";

drop index if exists "public"."idx_wbs_library_item_audit_changed_by";

drop index if exists "public"."idx_wbs_library_item_audit_client_id";

drop index if exists "public"."idx_wbs_library_item_audit_operation_type";

drop index if exists "public"."idx_wbs_library_item_audit_project_id";

drop index if exists "public"."idx_wbs_library_item_audit_wbs_library_item_id";

drop index if exists "public"."risk_register_project_idx";

CREATE INDEX approved_changes_audit_approved_change_id_idx ON public.approved_changes_audit USING btree (approved_change_id);

CREATE INDEX approved_changes_audit_changed_at_idx ON public.approved_changes_audit USING btree (changed_at);

CREATE INDEX approved_changes_audit_changed_by_idx ON public.approved_changes_audit USING btree (changed_by);

CREATE INDEX approved_changes_audit_project_id_idx ON public.approved_changes_audit USING btree (project_id);

CREATE INDEX approved_changes_original_risk_id_idx ON public.approved_changes USING btree (original_risk_id);

CREATE INDEX approved_changes_project_id_idx ON public.approved_changes USING btree (project_id);

CREATE INDEX budget_line_item_audit_budget_line_item_id_idx ON public.budget_line_item_audit USING btree (budget_line_item_id);

CREATE INDEX budget_line_item_audit_changed_at_idx ON public.budget_line_item_audit USING btree (changed_at);

CREATE INDEX budget_line_item_audit_changed_by_idx ON public.budget_line_item_audit USING btree (changed_by);

CREATE INDEX budget_line_item_audit_operation_type_idx ON public.budget_line_item_audit USING btree (operation_type);

CREATE INDEX budget_line_item_audit_project_id_idx ON public.budget_line_item_audit USING btree (project_id);

CREATE INDEX budget_line_item_current_project_id_idx ON public.budget_line_item_current USING btree (project_id);

CREATE UNIQUE INDEX budget_line_item_current_project_id_wbs_library_item_id_key ON public.budget_line_item_current USING btree (project_id, wbs_library_item_id);

CREATE INDEX budget_line_item_current_wbs_library_item_id_idx ON public.budget_line_item_current USING btree (wbs_library_item_id);

CREATE INDEX budget_snapshot_line_item_budget_snapshot_id_idx ON public.budget_snapshot_line_item USING btree (budget_snapshot_id);

CREATE INDEX budget_snapshot_line_item_wbs_library_item_id_idx ON public.budget_snapshot_line_item USING btree (wbs_library_item_id);

CREATE INDEX gateway_checklist_item_audit_changed_at_idx ON public.gateway_checklist_item_audit USING btree (changed_at);

CREATE INDEX gateway_checklist_item_audit_changed_by_idx ON public.gateway_checklist_item_audit USING btree (changed_by);

CREATE INDEX gateway_checklist_item_audit_gateway_checklist_item_id_idx ON public.gateway_checklist_item_audit USING btree (gateway_checklist_item_id);

CREATE INDEX project_gateway_stage_info_audit_changed_at_idx ON public.project_gateway_stage_info_audit USING btree (changed_at);

CREATE INDEX project_gateway_stage_info_audit_changed_by_idx ON public.project_gateway_stage_info_audit USING btree (changed_by);

CREATE INDEX project_gateway_stage_info_audit_project_gateway_stage_info_id_ ON public.project_gateway_stage_info_audit USING btree (project_gateway_stage_info_id);

CREATE INDEX project_stage_audit_changed_at_idx ON public.project_stage_audit USING btree (changed_at);

CREATE INDEX project_stage_audit_changed_by_idx ON public.project_stage_audit USING btree (changed_by);

CREATE INDEX project_stage_audit_operation_type_idx ON public.project_stage_audit USING btree (operation_type);

CREATE INDEX project_stage_audit_project_id_idx ON public.project_stage_audit USING btree (project_id);

CREATE INDEX project_stage_audit_project_stage_id_idx ON public.project_stage_audit USING btree (project_stage_id);

CREATE UNIQUE INDEX project_stage_project_id_stage_order_key ON public.project_stage USING btree (project_id, stage_order);

CREATE INDEX risk_register_audit_changed_at_idx ON public.risk_register_audit USING btree (changed_at);

CREATE INDEX risk_register_audit_changed_by_idx ON public.risk_register_audit USING btree (changed_by);

CREATE INDEX risk_register_audit_project_id_idx ON public.risk_register_audit USING btree (project_id);

CREATE INDEX risk_register_audit_risk_id_idx ON public.risk_register_audit USING btree (risk_id);

CREATE INDEX risk_register_project_id_idx ON public.risk_register USING btree (project_id);

CREATE INDEX risk_register_status_idx ON public.risk_register USING btree (status);

CREATE INDEX risk_register_wbs_library_item_id_idx ON public.risk_register USING btree (wbs_library_item_id);

CREATE INDEX wbs_library_item_audit_changed_at_idx ON public.wbs_library_item_audit USING btree (changed_at);

CREATE INDEX wbs_library_item_audit_changed_by_idx ON public.wbs_library_item_audit USING btree (changed_by);

CREATE INDEX wbs_library_item_audit_client_id_idx ON public.wbs_library_item_audit USING btree (client_id);

CREATE INDEX wbs_library_item_audit_operation_type_idx ON public.wbs_library_item_audit USING btree (operation_type);

CREATE INDEX wbs_library_item_audit_project_id_idx ON public.wbs_library_item_audit USING btree (project_id);

CREATE INDEX wbs_library_item_audit_wbs_library_item_id_idx ON public.wbs_library_item_audit USING btree (wbs_library_item_id);

CREATE INDEX wbs_library_item_parent_item_id_idx ON public.wbs_library_item USING btree (parent_item_id);

CREATE UNIQUE INDEX wbs_library_item_wbs_library_id_code_key ON public.wbs_library_item USING btree (wbs_library_id, code);

CREATE INDEX wbs_library_item_wbs_library_id_idx ON public.wbs_library_item USING btree (wbs_library_id);

CREATE UNIQUE INDEX wbs_library_name_key ON public.wbs_library USING btree (name);

alter table "public"."budget_line_item_current" add constraint "budget_line_item_current_project_id_wbs_library_item_id_key" UNIQUE using index "budget_line_item_current_project_id_wbs_library_item_id_key";

alter table "public"."project_stage" add constraint "project_stage_project_id_stage_order_key" UNIQUE using index "project_stage_project_id_stage_order_key";

alter table "public"."wbs_library" add constraint "wbs_library_name_key" UNIQUE using index "wbs_library_name_key";

alter table "public"."wbs_library_item" add constraint "wbs_library_item_wbs_library_id_code_key" UNIQUE using index "wbs_library_item_wbs_library_id_code_key";

alter table "public"."wbs_library_item" add constraint "wbs_library_item_client_id_fkey" FOREIGN KEY (client_id) REFERENCES client(client_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."wbs_library_item" validate constraint "wbs_library_item_client_id_fkey";

alter table "public"."wbs_library_item" add constraint "wbs_library_item_parent_item_id_fkey" FOREIGN KEY (parent_item_id) REFERENCES wbs_library_item(wbs_library_item_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."wbs_library_item" validate constraint "wbs_library_item_parent_item_id_fkey";

alter table "public"."wbs_library_item" add constraint "wbs_library_item_project_id_fkey" FOREIGN KEY (project_id) REFERENCES project(project_id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."wbs_library_item" validate constraint "wbs_library_item_project_id_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.add_creator_as_admin()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE 
	has_org_admin_access boolean := FALSE;
	org_id_var uuid;
BEGIN 
	-- Add the creator as an admin/owner
	IF TG_TABLE_NAME = 'organization' THEN 
		-- For organizations, simply add the creator as admin
		INSERT INTO public.membership(user_id, role, entity_type, entity_id)
		VALUES (
			NEW.created_by_user_id,
			'admin',
			'organization',
			NEW.org_id
		);
		
	ELSIF TG_TABLE_NAME = 'client' THEN 
		-- For clients, check if the creator already has admin access through the organization
		SELECT EXISTS (
			SELECT 1
			FROM public.membership m
			WHERE m.user_id = NEW.created_by_user_id
				AND m.entity_type = 'organization'
				AND m.entity_id = NEW.org_id
				AND m.role = 'admin'
		) INTO has_org_admin_access;
		
		IF NOT has_org_admin_access THEN
			INSERT INTO public.membership(user_id, role, entity_type, entity_id)
			VALUES (
				NEW.created_by_user_id,
				'admin',
				'client',
				NEW.client_id
			);
		END IF;
		
	ELSIF TG_TABLE_NAME = 'project' THEN 
		-- For projects, check if the creator already has admin access through client or organization
		SELECT EXISTS (
			SELECT 1
			FROM public.membership m
			WHERE m.user_id = NEW.created_by_user_id
				AND m.entity_type = 'client'
				AND m.entity_id = NEW.client_id
				AND m.role = 'admin'
		) INTO has_org_admin_access;
		
		-- Only add project owner membership if they don't have client admin access
		IF NOT has_org_admin_access THEN
			INSERT INTO public.membership(user_id, role, entity_type, entity_id)
			VALUES (
				NEW.created_by_user_id,
				'owner',
				'project',
				NEW.project_id
			);
		END IF;
	END IF;
	
	RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.apply_pending_invites()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
	v_invite public.invite %rowtype;
	v_entity_type public.entity_type;
	v_entity_id uuid;
	v_role public.membership_role;
	v_resource_type text;
BEGIN
	-- Loop through all pending invites for this email
	BEGIN
		FOR v_invite IN (
			SELECT *
			FROM public.invite
			WHERE lower(invitee_email) = lower(NEW.email)
				AND status = 'pending'
				AND expires_at > now()
		) LOOP
			BEGIN
				-- Store resource_type as text to avoid casting issues
				v_resource_type := v_invite.resource_type::text;

				IF v_resource_type = 'organization' THEN
					v_entity_type := 'organization'::public.entity_type;
					v_entity_id := v_invite.resource_id;
					IF v_invite.role = 'member' THEN
						v_role := 'viewer'::public.membership_role;
					ELSE
						v_role := v_invite.role::public.membership_role;
					END IF;
				ELSIF v_resource_type = 'client' THEN
					v_entity_type := 'client'::public.entity_type;
					v_entity_id := v_invite.resource_id;
					v_role := v_invite.role::public.membership_role;
				ELSIF v_resource_type = 'project' THEN
					v_entity_type := 'project'::public.entity_type;
					v_entity_id := v_invite.resource_id;
					v_role := v_invite.role::public.membership_role;
				ELSE
					CONTINUE;
				END IF;

				BEGIN
					INSERT INTO public.membership(user_id, role, entity_type, entity_id)
					VALUES (NEW.user_id, v_role, v_entity_type, v_entity_id)
					ON CONFLICT (entity_type, entity_id, user_id) DO NOTHING;

					UPDATE public.invite
					SET status = 'accepted'::public.invite_status,
						updated_at = now(),
						updated_by = NEW.user_id
					WHERE invite_id = v_invite.invite_id;
				EXCEPTION
					WHEN OTHERS THEN
						-- Log error but continue processing other invites
						RAISE NOTICE 'Error processing invite %: %', v_invite.invite_id, SQLERRM;
				END;
			EXCEPTION
				WHEN OTHERS THEN
					-- Log error but continue processing other invites
					RAISE NOTICE 'Error mapping invite %: %', v_invite.invite_id, SQLERRM;
			END;
		END LOOP;

		BEGIN
			UPDATE public.invite
			SET status = 'expired'::public.invite_status,
				updated_at = now(),
				updated_by = NEW.user_id
			WHERE lower(invitee_email) = lower(NEW.email)
				AND status = 'pending'
				AND expires_at <= now();
		EXCEPTION
			WHEN OTHERS THEN
				-- Log error but allow user creation to continue
				RAISE NOTICE 'Error expiring outdated invites: %', SQLERRM;
		END;
	EXCEPTION
		WHEN OTHERS THEN
			-- Log error but allow user creation to continue
			RAISE NOTICE 'Error in apply_pending_invites: %', SQLERRM;
	END;

	RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.audit_budget_line_item_changes()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        -- This handles cases like the generate_demo_budget_data() function
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.budget_line_item_audit (
            operation_type, changed_by, changed_at, old_values,
            budget_line_item_id, project_id, wbs_library_item_id, quantity, unit,
            material_rate, labor_rate, productivity_per_hour, unit_rate_manual_override,
            unit_rate, factor, remarks, cost_certainty, design_certainty, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.budget_line_item_id, OLD.project_id, OLD.wbs_library_item_id, OLD.quantity, OLD.unit,
            OLD.material_rate, OLD.labor_rate, OLD.productivity_per_hour, OLD.unit_rate_manual_override,
            OLD.unit_rate, OLD.factor, OLD.remarks, OLD.cost_certainty, OLD.design_certainty, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.budget_line_item_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            budget_line_item_id, project_id, wbs_library_item_id, quantity, unit,
            material_rate, labor_rate, productivity_per_hour, unit_rate_manual_override,
            unit_rate, factor, remarks, cost_certainty, design_certainty, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.budget_line_item_id, NEW.project_id, NEW.wbs_library_item_id, NEW.quantity, NEW.unit,
            NEW.material_rate, NEW.labor_rate, NEW.productivity_per_hour, NEW.unit_rate_manual_override,
            NEW.unit_rate, NEW.factor, NEW.remarks, NEW.cost_certainty, NEW.design_certainty, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.budget_line_item_audit (
            operation_type, changed_by, changed_at, new_values,
            budget_line_item_id, project_id, wbs_library_item_id, quantity, unit,
            material_rate, labor_rate, productivity_per_hour, unit_rate_manual_override,
            unit_rate, factor, remarks, cost_certainty, design_certainty, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.budget_line_item_id, NEW.project_id, NEW.wbs_library_item_id, NEW.quantity, NEW.unit,
            NEW.material_rate, NEW.labor_rate, NEW.productivity_per_hour, NEW.unit_rate_manual_override,
            NEW.unit_rate, NEW.factor, NEW.remarks, NEW.cost_certainty, NEW.design_certainty, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.audit_wbs_library_item_changes()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get the current user ID, fallback to a system user if not authenticated
    v_user_id := auth.uid();
    IF v_user_id IS NULL THEN
        -- Use a system user ID for operations not performed by authenticated users
        -- This handles cases like seed data or system operations
        v_user_id := '00000000-0000-0000-0000-000000000000'::UUID;
    END IF;

    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.wbs_library_item_audit (
            operation_type, changed_by, changed_at, old_values,
            wbs_library_item_id, wbs_library_id, level, in_level_code, parent_item_id,
            code, description, cost_scope, item_type, client_id, project_id, created_at, updated_at
        ) VALUES (
            'DELETE', v_user_id, NOW(), to_jsonb(OLD),
            OLD.wbs_library_item_id, OLD.wbs_library_id, OLD.level, OLD.in_level_code, OLD.parent_item_id,
            OLD.code, OLD.description, OLD.cost_scope, OLD.item_type, OLD.client_id, OLD.project_id, OLD.created_at, OLD.updated_at
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.wbs_library_item_audit (
            operation_type, changed_by, changed_at, old_values, new_values,
            wbs_library_item_id, wbs_library_id, level, in_level_code, parent_item_id,
            code, description, cost_scope, item_type, client_id, project_id, created_at, updated_at
        ) VALUES (
            'UPDATE', v_user_id, NOW(), to_jsonb(OLD), to_jsonb(NEW),
            NEW.wbs_library_item_id, NEW.wbs_library_id, NEW.level, NEW.in_level_code, NEW.parent_item_id,
            NEW.code, NEW.description, NEW.cost_scope, NEW.item_type, NEW.client_id, NEW.project_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.wbs_library_item_audit (
            operation_type, changed_by, changed_at, new_values,
            wbs_library_item_id, wbs_library_id, level, in_level_code, parent_item_id,
            code, description, cost_scope, item_type, client_id, project_id, created_at, updated_at
        ) VALUES (
            'INSERT', v_user_id, NOW(), to_jsonb(NEW),
            NEW.wbs_library_item_id, NEW.wbs_library_id, NEW.level, NEW.in_level_code, NEW.parent_item_id,
            NEW.code, NEW.description, NEW.cost_scope, NEW.item_type, NEW.client_id, NEW.project_id, NEW.created_at, NEW.updated_at
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_unit_item_cost(p_material_rate numeric, p_labor_rate numeric, p_productivity_per_hour numeric)
 RETURNS numeric
 LANGUAGE plpgsql
 SET search_path TO ''
AS $function$
DECLARE
	v_cost NUMERIC;
BEGIN
	-- Simple calculation for now - can be enhanced later
	v_cost := COALESCE(p_material_rate, 0);

	IF p_labor_rate IS NOT NULL
	AND p_productivity_per_hour IS NOT NULL
	AND p_productivity_per_hour > 0 THEN
		v_cost := v_cost + COALESCE(p_labor_rate, 0) / COALESCE(p_productivity_per_hour, 1);
	END IF;

	RETURN v_cost;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.can_access_client(client_id_param uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
	RETURN public.current_user_has_entity_access('client', client_id_param);
END;
$function$
;

CREATE OR REPLACE FUNCTION public.can_access_project(project_id_param uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
	RETURN public.current_user_has_entity_access('project', project_id_param);
END;
$function$
;

CREATE OR REPLACE FUNCTION public.can_modify_client(client_id_param uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
	RETURN public.current_user_has_entity_role('client', client_id_param, 'editor');
END;
$function$
;

CREATE OR REPLACE FUNCTION public.can_modify_client_wbs(client_id_param uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
	RETURN public.current_user_has_entity_role('client', client_id_param, 'admin');
END;
$function$
;

CREATE OR REPLACE FUNCTION public.can_modify_project(project_id_param uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
	RETURN public.current_user_has_entity_role('project', project_id_param, 'editor');
END;
$function$
;

CREATE OR REPLACE FUNCTION public.check_membership_redundancy()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE 
	ancestor_role public.membership_role;
BEGIN 
	-- Check if user already has equal or higher role through an ancestor
	IF NEW.entity_type = 'project' THEN 
		-- Check client level
		SELECT public.get_effective_role(NEW.user_id, 'client', p.client_id) INTO ancestor_role
		FROM public.project p
		WHERE p.project_id = NEW.entity_id;
		
		IF ancestor_role = 'admin' OR ancestor_role = 'owner' THEN 
			RAISE EXCEPTION 'User already has admin access to this project through client-level permissions';
		END IF;
		
		-- Check organization level
		SELECT public.get_effective_role(NEW.user_id, 'organization', c.org_id) INTO ancestor_role
		FROM public.project p
		JOIN public.client c ON p.client_id = c.client_id
		WHERE p.project_id = NEW.entity_id;
		
		IF ancestor_role = 'admin' THEN 
			RAISE EXCEPTION 'User already has admin access to this project through organization-level permissions';
		END IF;
		
	ELSIF NEW.entity_type = 'client' THEN 
		-- Check organization level
		SELECT public.get_effective_role(NEW.user_id, 'organization', c.org_id) INTO ancestor_role
		FROM public.client c
		WHERE c.client_id = NEW.entity_id;
		
		IF ancestor_role = 'admin' THEN 
			RAISE EXCEPTION 'User already has admin access to this client through organization-level permissions';
		END IF;
	END IF;
	
	RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.compare_budget_snapshots(p_snapshot_id_1 uuid, p_snapshot_id_2 uuid)
 RETURNS TABLE(wbs_library_item_id uuid, snapshot_1_quantity numeric, snapshot_1_cost numeric, snapshot_2_quantity numeric, snapshot_2_cost numeric, quantity_diff numeric, cost_diff numeric, percent_change numeric)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
	RETURN QUERY
	SELECT
		COALESCE(s1.wbs_library_item_id, s2.wbs_library_item_id) AS wbs_library_item_id,
		s1.quantity AS snapshot_1_quantity,
		s1.unit_rate AS snapshot_1_cost,
		s2.quantity AS snapshot_2_quantity,
		s2.unit_rate AS snapshot_2_cost,
		COALESCE(s2.quantity, 0) - COALESCE(s1.quantity, 0) AS quantity_diff,
		COALESCE(s2.unit_rate, 0) - COALESCE(s1.unit_rate, 0) AS cost_diff,
		CASE
			WHEN COALESCE(s1.unit_rate, 0) = 0 THEN NULL
			ELSE ((COALESCE(s2.unit_rate, 0) - COALESCE(s1.unit_rate, 0)) / s1.unit_rate) * 100
		END AS percent_change
	FROM public.budget_snapshot_line_item s1
	FULL OUTER JOIN public.budget_snapshot_line_item s2
		ON s1.wbs_library_item_id = s2.wbs_library_item_id
	WHERE s1.budget_snapshot_id = p_snapshot_id_1
	AND s2.budget_snapshot_id = p_snapshot_id_2;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.create_budget_snapshot(p_project_stage_id uuid, p_freeze_reason text DEFAULT NULL::text)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE 
	v_project_id UUID;
	v_snapshot_id UUID;
	v_item RECORD;
BEGIN 
	-- Get the project_id from the stage
	SELECT project_id INTO v_project_id
	FROM public.project_stage
	WHERE project_stage_id = p_project_stage_id;
	
	IF v_project_id IS NULL THEN 
		RAISE EXCEPTION 'Project stage not found';
	END IF;
	
	INSERT INTO public.budget_snapshot (
		project_stage_id,
		freeze_date,
		freeze_reason,
		created_by_user_id
	)
	VALUES (
		p_project_stage_id,
		now(),
		p_freeze_reason,
		auth.uid()
	)
	RETURNING budget_snapshot_id INTO v_snapshot_id;
	
	FOR v_item IN (
		SELECT *
		FROM public.budget_line_item_current
		WHERE project_id = v_project_id
	) LOOP
		INSERT INTO public.budget_snapshot_line_item (
			budget_snapshot_id,
			wbs_library_item_id,
			quantity,
			unit,
			material_rate,
			labor_rate,
			productivity_per_hour,
			unit_rate_manual_override,
			unit_rate,
			factor,
			remarks,
			cost_certainty,
			design_certainty
		)
		VALUES (
			v_snapshot_id,
			v_item.wbs_library_item_id,
			v_item.quantity,
			v_item.unit,
			v_item.material_rate,
			v_item.labor_rate,
			v_item.productivity_per_hour,
			v_item.unit_rate_manual_override,
			v_item.unit_rate,
			v_item.factor,
			v_item.remarks,
			v_item.cost_certainty,
			v_item.design_certainty
		);
	END LOOP;
	
	RETURN v_snapshot_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.current_user_has_entity_access(entity_type_param entity_type, entity_id_param uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$ 
BEGIN 
	RETURN public.has_entity_access(auth.uid(), entity_type_param, entity_id_param);
END;
$function$
;

CREATE OR REPLACE FUNCTION public.current_user_has_entity_role(entity_type_param entity_type, entity_id_param uuid, min_role_param membership_role)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
	RETURN public.has_entity_role(
		auth.uid(),
		entity_type_param,
		entity_id_param,
		min_role_param
	);
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_effective_role(user_id_param uuid, entity_type_param entity_type, entity_id_param uuid)
 RETURNS membership_role
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
	effective_role public.membership_role;
BEGIN
	-- Check for direct membership first
	SELECT role INTO effective_role
	FROM public.membership
	WHERE user_id = user_id_param
		AND entity_type = entity_type_param
		AND entity_id = entity_id_param;

	IF FOUND THEN
		RETURN effective_role;
	END IF;

	-- Check hierarchical access
	IF entity_type_param = 'project' THEN
		-- Check client level access
		SELECT m.role INTO effective_role
		FROM public.membership m
		JOIN public.project p ON p.project_id = entity_id_param
		WHERE m.user_id = user_id_param
			AND m.entity_type = 'client'
			AND m.entity_id = p.client_id;

		IF FOUND THEN
			RETURN effective_role;
		END IF;

		-- Check organization level access
		SELECT m.role INTO effective_role
		FROM public.membership m
		JOIN public.project p ON p.project_id = entity_id_param
		JOIN public.client c ON p.client_id = c.client_id
		WHERE m.user_id = user_id_param
			AND m.entity_type = 'organization'
			AND m.entity_id = c.org_id;

		IF FOUND THEN
			RETURN effective_role;
		END IF;

	ELSIF entity_type_param = 'client' THEN
		-- Check organization level access
		SELECT m.role INTO effective_role
		FROM public.membership m
		JOIN public.client c ON c.client_id = entity_id_param
		WHERE m.user_id = user_id_param
			AND m.entity_type = 'organization'
			AND m.entity_id = c.org_id;

		IF FOUND THEN
			RETURN effective_role;
		END IF;
	END IF;

	-- No access found
	RETURN NULL;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_entity_ancestors(entity_type_param entity_type, entity_id_param uuid)
 RETURNS TABLE(entity_type entity_type, entity_id uuid)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN 
	-- Return the entity itself
	RETURN QUERY SELECT entity_type_param, entity_id_param;
	
	-- Return ancestors based on entity type
	IF entity_type_param = 'project' THEN 
		-- Project -> Client -> Organization
		RETURN QUERY 
		SELECT 'client'::public.entity_type, p.client_id
		FROM public.project p
		WHERE p.project_id = entity_id_param;
		
		RETURN QUERY 
		SELECT 'organization'::public.entity_type, c.org_id
		FROM public.project p
		JOIN public.client c ON p.client_id = c.client_id
		WHERE p.project_id = entity_id_param;
		
	ELSIF entity_type_param = 'client' THEN 
		-- Client -> Organization
		RETURN QUERY 
		SELECT 'organization'::public.entity_type, c.org_id
		FROM public.client c
		WHERE c.client_id = entity_id_param;
	END IF;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$ 
BEGIN
	INSERT INTO public.profile (user_id, email, full_name)
	VALUES (
		NEW.id,
		NEW.email,
		NEW.raw_user_meta_data->>'full_name'
	);
	RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.has_entity_access(user_id_param uuid, entity_type_param entity_type, entity_id_param uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$ 
BEGIN 
	RETURN EXISTS (
		SELECT 1
		FROM public.membership m
		JOIN LATERAL public.get_entity_ancestors(entity_type_param, entity_id_param) a ON TRUE
		WHERE m.user_id = user_id_param
			AND m.entity_type = a.entity_type
			AND m.entity_id = a.entity_id
	);
END;
$function$
;

CREATE OR REPLACE FUNCTION public.has_entity_role(user_id_param uuid, entity_type_param entity_type, entity_id_param uuid, min_role_param membership_role)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
	user_role public.membership_role;
BEGIN
	user_role := public.get_effective_role(
		user_id_param,
		entity_type_param,
		entity_id_param
	);

	IF user_role IS NULL THEN
		RETURN FALSE;
	END IF;

	-- Role hierarchy: viewer < editor < admin < owner
	CASE min_role_param
		WHEN 'viewer' THEN
			RETURN user_role IN ('viewer', 'editor', 'admin', 'owner');
		WHEN 'editor' THEN
			RETURN user_role IN ('editor', 'admin', 'owner');
		WHEN 'admin' THEN
			RETURN user_role IN ('admin', 'owner');
		WHEN 'owner' THEN
			RETURN user_role = 'owner';
		ELSE
			RETURN FALSE;
	END CASE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.import_budget_data(p_project_id uuid, p_items jsonb)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
	v_item jsonb;
	v_result jsonb := '{"success": true, "imported": 0, "errors": []}'::jsonb;
	v_imported_count integer := 0;
	v_errors jsonb := '[]'::jsonb;
BEGIN
	-- Iterate through each item in the JSON array
	FOR v_item IN SELECT * FROM jsonb_array_elements(p_items)
	LOOP
		BEGIN
			-- Call upsert function for each item
			PERFORM public.upsert_budget_line_item(
				p_project_id,
				(v_item->>'wbs_library_item_id')::uuid,
				(v_item->>'quantity')::numeric,
				v_item->>'unit',
				COALESCE((v_item->>'material_rate')::numeric, 0),
				(v_item->>'labor_rate')::numeric,
				(v_item->>'productivity_per_hour')::numeric,
				COALESCE((v_item->>'unit_rate_manual_override')::boolean, false),
				COALESCE((v_item->>'unit_rate')::numeric, 0),
				(v_item->>'factor')::numeric,
				v_item->>'remarks',
				(v_item->>'cost_certainty')::numeric,
				(v_item->>'design_certainty')::numeric,
				'Imported from budget data'
			);

			v_imported_count := v_imported_count + 1;

		EXCEPTION
			WHEN OTHERS THEN
				v_errors := v_errors || jsonb_build_object(
					'item', v_item,
					'error', SQLERRM
				);
		END;
	END LOOP;

	-- Build result
	v_result := jsonb_set(v_result, '{imported}', to_jsonb(v_imported_count));
	v_result := jsonb_set(v_result, '{errors}', v_errors);

	IF jsonb_array_length(v_errors) > 0 THEN
		v_result := jsonb_set(v_result, '{success}', 'false'::jsonb);
	END IF;

	RETURN v_result;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.is_client_admin(client_id_param uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
	org_id_var uuid;
BEGIN
	-- Check if user has direct admin role on the client
	IF public.current_user_has_entity_role('client', client_id_param, 'admin') THEN
		RETURN TRUE;
	END IF;

	-- Check if user has admin role on the organization
	SELECT org_id INTO org_id_var
	FROM public.client
	WHERE client_id = client_id_param;

	IF org_id_var IS NOT NULL AND public.current_user_has_entity_role('organization', org_id_var, 'admin') THEN
		RETURN TRUE;
	END IF;

	RETURN FALSE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.is_org_admin_for_project(project_id_param uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
	org_id_var uuid;
BEGIN
	SELECT c.org_id INTO org_id_var
	FROM public.project p
	JOIN public.client c ON p.client_id = c.client_id
	WHERE p.project_id = project_id_param;

	IF org_id_var IS NOT NULL THEN
		RETURN public.current_user_has_entity_role('organization', org_id_var, 'admin');
	END IF;

	RETURN FALSE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.is_project_owner(project_id_param uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
	client_id_var uuid;
	org_id_var uuid;
BEGIN
	-- Check if user has direct owner role on the project
	IF public.current_user_has_entity_role('project', project_id_param, 'owner') THEN
		RETURN TRUE;
	END IF;

	-- Check if user has admin role on the client
	SELECT client_id INTO client_id_var
	FROM public.project
	WHERE project_id = project_id_param;

	IF client_id_var IS NOT NULL
	AND public.current_user_has_entity_role('client', client_id_var, 'admin') THEN
		RETURN TRUE;
	END IF;

	-- Check if user has admin role on the organization
	SELECT org_id INTO org_id_var
	FROM public.client
	WHERE client_id = client_id_var;

	IF org_id_var IS NOT NULL
	AND public.current_user_has_entity_role('organization', org_id_var, 'admin') THEN
		RETURN TRUE;
	END IF;

	RETURN FALSE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.is_stage_ready_for_completion(p_project_stage_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE 
	incomplete_count INTEGER;
BEGIN 
	-- Count incomplete checklist items for this stage
	SELECT COUNT(*) INTO incomplete_count
	FROM public.gateway_checklist_item gci
	JOIN public.gateway_checklist_item_status_log gcisl ON gci.gateway_checklist_item_id = gcisl.gateway_checklist_item_id
	WHERE gci.project_stage_id = p_project_stage_id
		AND gcisl.latest = TRUE
		AND gcisl.status = 'Incomplete';
	
	-- Stage is ready if there are no incomplete items
	RETURN incomplete_count = 0;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.log_initial_checklist_item_status()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$ 
BEGIN 
	-- Insert a new status log entry for the newly created checklist item
	INSERT INTO public.gateway_checklist_item_status_log (
		gateway_checklist_item_id,
		status,
		updated_by_user_id,
		valid_at,
		latest
	)
	VALUES (
		NEW.gateway_checklist_item_id,
		'Incomplete', -- Default initial status
		auth.uid(), -- Current user who created the item
		now(), -- Current timestamp
		TRUE -- This is the latest status since it's the first one
	);
	
	RETURN NULL;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.revert_to_budget_snapshot(p_budget_snapshot_id uuid, p_revert_reason text DEFAULT 'Reverted to snapshot'::text)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
	v_project_id UUID;
	v_item RECORD;
BEGIN
	-- Get the project_id from the snapshot and stage
	SELECT project_id INTO v_project_id
	FROM public.project_stage ps
	JOIN public.budget_snapshot bs ON ps.project_stage_id = bs.project_stage_id
	WHERE bs.budget_snapshot_id = p_budget_snapshot_id;

	IF v_project_id IS NULL THEN
		RAISE EXCEPTION 'Budget snapshot not found or not linked to a valid project';
	END IF;

	FOR v_item IN (
		SELECT *
		FROM public.budget_snapshot_line_item
		WHERE budget_snapshot_id = p_budget_snapshot_id
	) LOOP
		-- Use the upsert function for each item
		PERFORM public.upsert_budget_line_item(
			v_project_id,
			v_item.wbs_library_item_id,
			v_item.quantity,
			v_item.unit,
			v_item.material_rate,
			v_item.labor_rate,
			v_item.productivity_per_hour,
			v_item.unit_rate_manual_override,
			v_item.unit_rate,
			v_item.factor,
			v_item.remarks,
			v_item.cost_certainty,
			v_item.design_certainty,
			p_revert_reason,
			NULL -- We want to create new records when reverting, not update existing ones
		);
	END LOOP;

	RETURN TRUE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.set_gateway_checklist_item_latest()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN 
	-- Set all previous entries for this checklist item to not latest
	UPDATE public.gateway_checklist_item_status_log
	SET latest = FALSE
	WHERE gateway_checklist_item_id = NEW.gateway_checklist_item_id
		AND log_id != NEW.log_id;
	
	-- Set the new entry as latest
	UPDATE public.gateway_checklist_item_status_log
	SET latest = TRUE
	WHERE log_id = NEW.log_id;
	
	RETURN NULL;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_updated_at_column()
 RETURNS trigger
 LANGUAGE plpgsql
 SET search_path TO ''
AS $function$ 
BEGIN 
	NEW.updated_at = timezone('utc', now());
	RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.upsert_budget_line_item(p_project_id uuid, p_wbs_library_item_id uuid, p_quantity numeric, p_unit text DEFAULT NULL::text, p_material_rate numeric DEFAULT 0, p_labor_rate numeric DEFAULT NULL::numeric, p_productivity_per_hour numeric DEFAULT NULL::numeric, p_unit_rate_manual_override boolean DEFAULT false, p_unit_rate numeric DEFAULT 0, p_factor numeric DEFAULT NULL::numeric, p_remarks text DEFAULT NULL::text, p_cost_certainty numeric DEFAULT NULL::numeric, p_design_certainty numeric DEFAULT NULL::numeric, p_change_reason text DEFAULT NULL::text, p_budget_line_item_id uuid DEFAULT NULL::uuid)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
DECLARE
	v_budget_line_item_id UUID;
	v_cost_to_use NUMERIC;
BEGIN
	-- Determine which cost to use
	IF p_unit_rate_manual_override THEN
		v_cost_to_use := p_unit_rate;
	ELSE
		v_cost_to_use := COALESCE(p_material_rate, 0) +
			CASE
				WHEN p_labor_rate IS NOT NULL AND p_productivity_per_hour IS NOT NULL
				THEN p_labor_rate / p_productivity_per_hour
				ELSE 0
			END;
	END IF;

	-- If budget_line_item_id is provided, try to update existing record
	IF p_budget_line_item_id IS NOT NULL THEN
		UPDATE public.budget_line_item_current
		SET
			quantity = p_quantity,
			unit = p_unit,
			material_rate = p_material_rate,
			labor_rate = p_labor_rate,
			productivity_per_hour = p_productivity_per_hour,
			unit_rate_manual_override = p_unit_rate_manual_override,
			unit_rate = v_cost_to_use,
			factor = p_factor,
			remarks = p_remarks,
			cost_certainty = p_cost_certainty,
			design_certainty = p_design_certainty,
			updated_at = now()
		WHERE budget_line_item_id = p_budget_line_item_id
		RETURNING budget_line_item_id INTO v_budget_line_item_id;

		IF FOUND THEN
			RETURN v_budget_line_item_id;
		END IF;
	END IF;

	-- Insert new record
	INSERT INTO public.budget_line_item_current (
		project_id,
		wbs_library_item_id,
		quantity,
		unit,
		material_rate,
		labor_rate,
		productivity_per_hour,
		unit_rate_manual_override,
		unit_rate,
		factor,
		remarks,
		cost_certainty,
		design_certainty
	)
	VALUES (
		p_project_id,
		p_wbs_library_item_id,
		p_quantity,
		p_unit,
		p_material_rate,
		p_labor_rate,
		p_productivity_per_hour,
		p_unit_rate_manual_override,
		v_cost_to_use,
		p_factor,
		p_remarks,
		p_cost_certainty,
		p_design_certainty
	)
	ON CONFLICT (project_id, wbs_library_item_id) DO UPDATE SET
		quantity = EXCLUDED.quantity,
		unit = EXCLUDED.unit,
		material_rate = EXCLUDED.material_rate,
		labor_rate = EXCLUDED.labor_rate,
		productivity_per_hour = EXCLUDED.productivity_per_hour,
		unit_rate_manual_override = EXCLUDED.unit_rate_manual_override,
		unit_rate = EXCLUDED.unit_rate,
		factor = EXCLUDED.factor,
		remarks = EXCLUDED.remarks,
		cost_certainty = EXCLUDED.cost_certainty,
		design_certainty = EXCLUDED.design_certainty,
		updated_at = now()
	RETURNING budget_line_item_id INTO v_budget_line_item_id;

	RETURN v_budget_line_item_id;
END;
$function$
;

create policy "Organization editors and admins can insert clients"
on "public"."client"
as permissive
for insert
to authenticated
with check (current_user_has_entity_role('organization'::entity_type, org_id, 'editor'::membership_role));


create policy "Organization editors and admins can update clients"
on "public"."client"
as permissive
for update
to authenticated
using (current_user_has_entity_role('organization'::entity_type, org_id, 'editor'::membership_role))
with check (current_user_has_entity_role('organization'::entity_type, org_id, 'editor'::membership_role));


create policy "Project editors can delete gateway checklist item"
on "public"."gateway_checklist_item"
as permissive
for delete
to authenticated
using (( SELECT can_modify_project(( SELECT project_stage.project_id
           FROM project_stage
          WHERE (project_stage.project_stage_id = gateway_checklist_item.project_stage_id))) AS can_modify_project));


create policy "Project editors can insert gateway checklist item"
on "public"."gateway_checklist_item"
as permissive
for insert
to authenticated
with check (( SELECT can_modify_project(( SELECT project_stage.project_id
           FROM project_stage
          WHERE (project_stage.project_stage_id = gateway_checklist_item.project_stage_id))) AS can_modify_project));


create policy "Project editors can update gateway checklist item"
on "public"."gateway_checklist_item"
as permissive
for update
to authenticated
using (( SELECT can_modify_project(( SELECT project_stage.project_id
           FROM project_stage
          WHERE (project_stage.project_stage_id = gateway_checklist_item.project_stage_id))) AS can_modify_project))
with check (( SELECT can_modify_project(( SELECT project_stage.project_id
           FROM project_stage
          WHERE (project_stage.project_stage_id = gateway_checklist_item.project_stage_id))) AS can_modify_project));


create policy "Admins can manage invites"
on "public"."invite"
as permissive
for insert
to authenticated
with check (current_user_has_entity_role(((resource_type)::text)::entity_type, resource_id, 'admin'::membership_role));


create policy "Admins can update invites"
on "public"."invite"
as permissive
for update
to authenticated
using (current_user_has_entity_role(((resource_type)::text)::entity_type, resource_id, 'admin'::membership_role))
with check (current_user_has_entity_role(((resource_type)::text)::entity_type, resource_id, 'admin'::membership_role));


create policy "Users can view invites for entities they have access to"
on "public"."invite"
as permissive
for select
to authenticated
using (current_user_has_entity_access(((resource_type)::text)::entity_type, resource_id));


create policy "Users can view memberships for entities they have access to"
on "public"."membership"
as permissive
for select
to authenticated
using (current_user_has_entity_access(entity_type, entity_id));


create policy "System can insert wbs library item audit records"
on "public"."wbs_library_item_audit"
as permissive
for insert
to service_role
with check (true);


create policy "Users can view wbs library item audit for accessible items"
on "public"."wbs_library_item_audit"
as permissive
for select
to authenticated
using (
CASE
    WHEN (item_type = 'standard'::text) THEN true
    WHEN ((item_type = 'custom'::text) AND (client_id IS NOT NULL)) THEN can_access_client(client_id)
    WHEN ((item_type = 'custom'::text) AND (project_id IS NOT NULL)) THEN can_access_project(project_id)
    ELSE false
END);


create policy "Project editors can update budget line item"
on "public"."budget_line_item_current"
as permissive
for update
to authenticated
using (( SELECT can_modify_project(budget_line_item_current.project_id) AS can_modify_project))
with check (( SELECT can_modify_project(budget_line_item_current.project_id) AS can_modify_project));


create policy "Project editors can update budget snapshot"
on "public"."budget_snapshot"
as permissive
for update
to authenticated
using (( SELECT can_modify_project(( SELECT project_stage.project_id
           FROM project_stage
          WHERE (project_stage.project_stage_id = budget_snapshot.project_stage_id))) AS can_modify_project))
with check (( SELECT can_modify_project(( SELECT project_stage.project_id
           FROM project_stage
          WHERE (project_stage.project_stage_id = budget_snapshot.project_stage_id))) AS can_modify_project));


create policy "Project editors can update budget snapshot line item"
on "public"."budget_snapshot_line_item"
as permissive
for update
to authenticated
using (( SELECT can_modify_project(( SELECT project_stage.project_id
           FROM project_stage
          WHERE (project_stage.project_stage_id = ( SELECT budget_snapshot.project_stage_id
                   FROM budget_snapshot
                  WHERE (budget_snapshot.budget_snapshot_id = budget_snapshot_line_item.budget_snapshot_id))))) AS can_modify_project))
with check (( SELECT can_modify_project(( SELECT project_stage.project_id
           FROM project_stage
          WHERE (project_stage.project_stage_id = ( SELECT budget_snapshot.project_stage_id
                   FROM budget_snapshot
                  WHERE (budget_snapshot.budget_snapshot_id = budget_snapshot_line_item.budget_snapshot_id))))) AS can_modify_project));


create policy "Users can view clients they have access to"
on "public"."client"
as permissive
for select
to authenticated
using (current_user_has_entity_access('client'::entity_type, client_id));


create policy "Users can view gateway checklist item audit for accessible proj"
on "public"."gateway_checklist_item_audit"
as permissive
for select
to authenticated
using (( SELECT can_access_project(( SELECT project_stage.project_id
           FROM project_stage
          WHERE (project_stage.project_stage_id = gateway_checklist_item_audit.project_stage_id))) AS can_access_project));


create policy "Admins can delete invites"
on "public"."invite"
as permissive
for delete
to authenticated
using ((((resource_type = 'organization'::invite_resource_type) AND current_user_has_entity_role('organization'::entity_type, resource_id, 'admin'::membership_role)) OR ((resource_type = 'client'::invite_resource_type) AND current_user_has_entity_role('client'::entity_type, resource_id, 'admin'::membership_role)) OR ((resource_type = 'project'::invite_resource_type) AND current_user_has_entity_role('project'::entity_type, resource_id, 'admin'::membership_role))));


create policy "Users can view organizations they have access to"
on "public"."organization"
as permissive
for select
to authenticated
using (current_user_has_entity_access('organization'::entity_type, org_id));


create policy "Users can insert gateway stage info for projects they can edit"
on "public"."project_gateway_stage_info"
as permissive
for insert
to authenticated
with check ((EXISTS ( SELECT 1
   FROM project_stage ps
  WHERE ((ps.project_stage_id = project_gateway_stage_info.project_stage_id) AND can_modify_project(ps.project_id)))));


create policy "Users can view project gateway stage info audit for accessible "
on "public"."project_gateway_stage_info_audit"
as permissive
for select
to authenticated
using (( SELECT can_access_project(( SELECT project_stage.project_id
           FROM project_stage
          WHERE (project_stage.project_stage_id = project_gateway_stage_info_audit.project_stage_id))) AS can_access_project));


create policy "Project Editors and Owners can update project stage"
on "public"."project_stage"
as permissive
for update
to authenticated
using (( SELECT can_modify_project(project_stage.project_id) AS can_modify_project))
with check (( SELECT can_modify_project(project_stage.project_id) AS can_modify_project));


CREATE TRIGGER update_updated_at BEFORE UPDATE ON public.approved_changes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_updated_at BEFORE UPDATE ON public.budget_line_item_current FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER add_creator_as_admin_client AFTER INSERT ON public.client FOR EACH ROW EXECUTE FUNCTION add_creator_as_admin();

CREATE TRIGGER add_creator_as_admin_project AFTER INSERT ON public.project FOR EACH ROW EXECUTE FUNCTION add_creator_as_admin();

CREATE TRIGGER update_updated_at BEFORE UPDATE ON public.risk_register FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();


